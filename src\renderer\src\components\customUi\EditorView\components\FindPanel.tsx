import React, { useState, useEffect, useRef, useCallback } from 'react'; // Added useCallback
import { Block, BlockNoteEditor } from '@blocknote/core'; // Added Block for type usage
import { Input } from '@/ui/input';
import { Button } from '@/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/ui/tooltip';
import {
  Search,
  ChevronUp,
  ChevronDown,
  X,
  Replace,
  List,
  CaseSensitive
} from 'lucide-react';
import { cn } from '@/lib/utils';
import {
  findInDocument,
  navigateToMatch,
  createFindResult,
  getNextMatchIndex,
  getPreviousMatchIndex,
  replaceInDocument,
  replaceAllInDocument,
  extractTextFromBlock, // Ensure this is imported
  type FindResult,
  type FindMatch
} from '../utils/findUtils';

interface FindPanelProps {
  isOpen: boolean;
  onClose: () => void;
  editor: BlockNoteEditor | null;
}

export const FindPanel: React.FC<FindPanelProps> = ({
  isOpen,
  onClose,
  editor
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [replaceTerm, setReplaceTerm] = useState('');
  const [caseSensitive, setCaseSensitive] = useState(false);
  const [showReplace, setShowReplace] = useState(false);
  const [showResults, setShowResults] = useState(true);
  const [findResult, setFindResult] = useState<FindResult>({
    matches: [],
    currentMatchIndex: -1,
    totalMatches: 0
  });

  const searchInputRef = useRef<HTMLInputElement>(null);
  const replaceInputRef = useRef<HTMLInputElement>(null);
  const lastSearchTermRef = useRef<string>('');
  // Use a ref for the debounce timeout ID to persist it across re-renders without causing effect re-runs
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);


  const applyHighlights = useCallback((matches: FindMatch[], currentIndex: number) => {
    console.log(`[FindPanel] 🎯 applyHighlights called with: ${matches.length} matches, current: ${currentIndex}`);

    try {
      // First, clear all existing highlights
      const allBlocks = document.querySelectorAll('.bn-block-outer');
      console.log(`[FindPanel] 🧹 Found ${allBlocks.length} blocks to clear highlights from`);

      allBlocks.forEach(block => {
        block.classList.remove('find-block-highlight', 'find-block-highlight-current');
      });

      if (matches.length === 0) {
        console.log('[FindPanel] ℹ️ No matches to highlight, returning early');
        return;
      }

      // Get unique block IDs and current block ID
      const blockIds = new Set(matches.map(match => match.blockId));
      console.log(`[FindPanel] 🔢 Unique block IDs to highlight: ${blockIds.size}`);

      const currentBlockId = currentIndex >= 0 && matches[currentIndex] ? matches[currentIndex].blockId : null;
      console.log(`[FindPanel] 🎯 Current block ID: ${currentBlockId}`);

      // Apply highlights to matching blocks
      blockIds.forEach(blockId => {
        console.log(`[FindPanel] 🔍 Looking for block with ID: ${blockId}`);

        // Try different selector approaches
        const element = document.querySelector(`.bn-block-outer[data-id="${blockId}"]`);
        const elementAlt = document.querySelector(`[data-id="${blockId}"].bn-block-outer`);
        const elementGeneric = document.querySelector(`[data-id="${blockId}"]`);

        console.log(`[FindPanel] 🔍 Selector results:`, {
          mainSelector: !!element,
          altSelector: !!elementAlt,
          genericSelector: !!elementGeneric,
          genericType: elementGeneric ? elementGeneric.tagName : 'N/A',
          genericClasses: elementGeneric ? Array.from(elementGeneric.classList) : []
        });

        if (element) {
          const isCurrent = blockId === currentBlockId;
          const className = isCurrent ? 'find-block-highlight-current' : 'find-block-highlight';
          element.classList.add(className);
          console.log(`[FindPanel] ✅ Applied ${className} to ${blockId}`);

          // Debug the element's classes after adding
          console.log(`[FindPanel] 📋 Element classes after adding:`, Array.from(element.classList));
        } else {
          console.warn(`[FindPanel] ⚠️ Element NOT FOUND for blockId: ${blockId} during applyHighlights`);

          // Try to find any elements with data-id attribute for debugging
          const allDataIdElements = document.querySelectorAll('[data-id]');
          console.log(`[FindPanel] 🔍 Found ${allDataIdElements.length} elements with data-id attribute`);
          if (allDataIdElements.length < 10) {
            console.log('[FindPanel] 📋 All data-id elements:', Array.from(allDataIdElements).map(el => ({
              id: el.getAttribute('data-id'),
              classes: Array.from(el.classList),
              tagName: el.tagName
            })));
          }
        }
      });
    } catch (error) {
      console.error('[FindPanel] 💥 Error in applyHighlights:', error);
    }
  }, []); // No dependencies, relies on arguments

  const clearHighlights = useCallback(() => {
    console.log('[FindPanel] 🧹 clearHighlights called');

    try {
      const highlightedBlocks = document.querySelectorAll('.find-block-highlight, .find-block-highlight-current');
      console.log(`[FindPanel] 🧹 Found ${highlightedBlocks.length} highlighted blocks to clear`);

      document.querySelectorAll('.bn-block-outer').forEach(block => {
        block.classList.remove('find-block-highlight', 'find-block-highlight-current');
      });

      console.log('[FindPanel] ✅ Cleared all highlights');
    } catch (error) {
      console.error('[FindPanel] 💥 Error in clearHighlights:', error);
    }
  }, []);

  const executeSearch = useCallback(() => {
    console.log('[FindPanel] 🔍 executeSearch called with:', {
      searchTerm,
      caseSensitive,
      editorExists: !!editor
    });

    if (!editor || !searchTerm.trim()) {
      console.log('[FindPanel] ⚠️ Search aborted: editor missing or empty search term');
      clearHighlights();
      setFindResult({ matches: [], currentMatchIndex: -1, totalMatches: 0 });
      lastSearchTermRef.current = ''; // Clear last search term
      return;
    }

    console.log('[FindPanel] 📄 Editor document blocks:', editor.document.length);

    try {
      // Perform the search
      console.log('[FindPanel] 🔎 Calling findInDocument with:', searchTerm, caseSensitive);
      const matches = findInDocument(editor.document, searchTerm, caseSensitive);
      console.log('[FindPanel] ✅ findInDocument returned matches:', matches.length);
      console.log('[FindPanel] 📊 Match details:', matches.map(m => ({
        blockId: m.blockId,
        textIndex: m.textIndex,
        text: m.text.substring(0, 30) + (m.text.length > 30 ? '...' : '')
      })));

      // Create result object
      const result = createFindResult(matches, matches.length > 0 ? 0 : -1);
      console.log('[FindPanel] 📋 Created findResult:', result);
      setFindResult(result);
      lastSearchTermRef.current = searchTerm;

      // Apply highlights
      console.log('[FindPanel] 🎨 Calling applyHighlights with matches:', matches.length);
      applyHighlights(result.matches, result.currentMatchIndex);

      // Navigate to first match if available
      if (result.matches.length > 0 && result.currentMatchIndex !== -1) {
        console.log('[FindPanel] 🚀 Navigating to first match:', result.matches[result.currentMatchIndex]);
        navigateToMatch(editor, result.matches[result.currentMatchIndex]);
      }
    } catch (error) {
      console.error('[FindPanel] 💥 Error during search execution:', error);
    }
  }, [editor, searchTerm, caseSensitive, clearHighlights, applyHighlights]);

  const goToNext = useCallback(() => {
    if (!editor || findResult.totalMatches === 0) return;
    const nextIndex = getNextMatchIndex(findResult.currentMatchIndex, findResult.totalMatches);
    setFindResult(prev => ({ ...prev, currentMatchIndex: nextIndex }));
    applyHighlights(findResult.matches, nextIndex);
    if (findResult.matches[nextIndex]) {
      navigateToMatch(editor, findResult.matches[nextIndex]);
    }
  }, [editor, findResult, applyHighlights]);

  const goToPrevious = useCallback(() => {
    if (!editor || findResult.totalMatches === 0) return;
    const prevIndex = getPreviousMatchIndex(findResult.currentMatchIndex, findResult.totalMatches);
    setFindResult(prev => ({ ...prev, currentMatchIndex: prevIndex }));
    applyHighlights(findResult.matches, prevIndex);
    if (findResult.matches[prevIndex]) {
      navigateToMatch(editor, findResult.matches[prevIndex]);
    }
  }, [editor, findResult, applyHighlights]);

  const goToMatch = useCallback((matchIndex: number) => {
    if (!editor || !findResult.matches[matchIndex]) return;
    setFindResult(prev => ({ ...prev, currentMatchIndex: matchIndex }));
    applyHighlights(findResult.matches, matchIndex);
    navigateToMatch(editor, findResult.matches[matchIndex]);
  }, [editor, findResult, applyHighlights]);

  const replaceCurrentMatch = useCallback(() => {
    if (!editor || findResult.totalMatches === 0 || findResult.currentMatchIndex === -1 || !replaceTerm) return;
    const currentMatch = findResult.matches[findResult.currentMatchIndex];
    if (!currentMatch) return;
    replaceInDocument(editor, currentMatch, replaceTerm);
    setTimeout(() => executeSearch(), 100); // Re-search
  }, [editor, findResult, replaceTerm, executeSearch]);

  const replaceAllMatches = useCallback(() => {
    if (!editor || findResult.totalMatches === 0 || !replaceTerm) return;
    replaceAllInDocument(editor, findResult.matches, replaceTerm);
    setTimeout(() => executeSearch(), 100); // Re-search
  }, [editor, findResult, replaceTerm, executeSearch]);

  useEffect(() => {
    if (!isOpen) {
      clearHighlights();
      setSearchTerm('');
      setReplaceTerm('');
      setShowReplace(false);
      setShowResults(true);
      setFindResult({ matches: [], currentMatchIndex: -1, totalMatches: 0 });
      lastSearchTermRef.current = '';
    } else if (searchInputRef.current) {
        setTimeout(() => {
            searchInputRef.current?.focus();
            searchInputRef.current?.select();
        }, 100);
    }
  }, [isOpen, clearHighlights]);


  // ✨ CLEANUP EFFECT - Using editor.onChange for BlockNote
  useEffect(() => {
    if (!editor || !lastSearchTermRef.current) {
        // If there was a timeout scheduled from a previous run of this effect, clear it.
        if (debounceTimeoutRef.current) {
            clearTimeout(debounceTimeoutRef.current);
            debounceTimeoutRef.current = null;
        }
        return;
    }

    const cleanupInvalidHighlights = () => {
      if (!editor) return; // Guard: editor might have become null

      const searchText = caseSensitive ? lastSearchTermRef.current : lastSearchTermRef.current.toLowerCase();
      if (!searchText) { // If search text is empty, clear all highlights
        clearHighlights();
        return;
      }

      const currentEditorBlocks = editor.document;
      const blockMap = new Map(currentEditorBlocks.map(b => [b.id, b]));

      document.querySelectorAll('.bn-block-outer.find-block-highlight, .bn-block-outer.find-block-highlight-current').forEach(element => {
        const blockId = element.getAttribute('data-id');
        if (!blockId) return;

        const blockObject = blockMap.get(blockId);

        if (blockObject) {
          const extractedBlockText = extractTextFromBlock(blockObject);
          const textToSearch = caseSensitive ? extractedBlockText : extractedBlockText.toLowerCase();

          if (!textToSearch.includes(searchText)) {
            element.classList.remove('find-block-highlight', 'find-block-highlight-current');
            console.log(`[FindPanel] 🧹 Removed highlight from modified block ID: ${blockId} (using extractTextFromBlock)`);
          }
        } else {
          element.classList.remove('find-block-highlight', 'find-block-highlight-current');
          console.log(`[FindPanel] 🧹 Removed highlight from block ID: ${blockId} (block no longer in editor document)`);
        }
      });
    };

    const debouncedCleanup = () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
      debounceTimeoutRef.current = setTimeout(cleanupInvalidHighlights, 300); // Debounce time
    };

    // Subscribe to BlockNote editor changes
    // editor.onChange returns an unsubscribe function
    const unsubscribe = editor.onChange(() => {
      debouncedCleanup();
    });

    // Cleanup function for when the effect re-runs or component unmounts
    return () => {
      unsubscribe(); // Unsubscribe from editor changes
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current); // Clear any pending debounce timeout
        debounceTimeoutRef.current = null;
      }
      // console.log('[FindPanel] Cleaned up editor change listener for highlights.');
    };
  // IMPORTANT: Add extractTextFromBlock to dependency array if it's not stable (i.e. not a top-level import)
  // Assuming extractTextFromBlock is imported and stable, it doesn't need to be in deps.
  // If it were defined inside FindPanel, it would.
  }, [editor, caseSensitive, lastSearchTermRef, clearHighlights]);


  const handleSearchKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      if (e.shiftKey) {
        goToPrevious();
      } else if (findResult.totalMatches > 0 && searchTerm === lastSearchTermRef.current) { // Only navigate if search term hasn't changed
        goToNext();
      } else {
        executeSearch();
      }
    } else if (e.key === 'Escape') {
      e.preventDefault();
      onClose();
    }
  };

  const handleReplaceKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      replaceCurrentMatch();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      onClose();
    }
  };

  const testSimpleHighlight = () => {
    const firstBlock = document.querySelector('.bn-block-outer');
    if (firstBlock) {
      firstBlock.classList.add('find-block-highlight-current');
      console.log('✅ Test highlight applied:', firstBlock);
    }
  };

  const debugCurrentState = () => {
    console.log('=== DEBUG STATE ===');
    console.log('Search term:', searchTerm);
    console.log('Last search term ref:', lastSearchTermRef.current);
    console.log('Find result:', findResult);
    console.log('Highlighted elements:', document.querySelectorAll('.find-block-highlight, .find-block-highlight-current').length);
    findResult.matches.forEach((match, index) => {
      const element = document.querySelector(`[data-id="${match.blockId}"].bn-block-outer`);
      console.log(`Match ${index} (ID: ${match.blockId}):`, {
        foundInDOM: !!element,
        classes: element ? Array.from(element.classList) : 'NOT_FOUND'
      });
    });
  };

  const displayText = findResult.totalMatches > 0
    ? `${findResult.currentMatchIndex + 1} of ${findResult.totalMatches}`
    : findResult.totalMatches === 0 && lastSearchTermRef.current && searchTerm === lastSearchTermRef.current // Only show "No matches" if search was executed for current term
      ? 'No matches'
      : '';

  if (!isOpen) return null;

  return (
    <TooltipProvider delayDuration={100}>
      <div className="fixed top-4 right-4 z-[1000] bg-background border border-border rounded-lg shadow-lg min-w-80 max-w-96">
        <div className="flex items-center justify-between p-3 border-b border-border">
          <div className="flex items-center gap-2">
            <Search className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm font-medium">Find & Replace</span>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose} className="h-6 w-6 p-0">
            <X className="h-3 w-3" />
          </Button>
        </div>

        <div className="p-3 space-y-3">
          <div className="flex items-center gap-2">
            <div className="relative flex-1">
              <Input
                ref={searchInputRef}
                type="text"
                placeholder="Find... (Enter to search)"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyDown={handleSearchKeyDown}
                className="h-8 text-sm pr-24"
              />
              <div className="absolute right-1 top-1/2 -translate-y-1/2 flex items-center gap-1">
                <span className="text-xs text-muted-foreground min-w-16 text-center">
                  {displayText}
                </span>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={executeSearch}
              className="h-8 px-2 text-xs"
              disabled={!searchTerm.trim()}
            >
              Search
            </Button>
            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={goToPrevious}
                disabled={findResult.totalMatches === 0}
                className="h-8 w-8 p-0"
              >
                <ChevronUp className="h-3 w-3" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={goToNext}
                disabled={findResult.totalMatches === 0}
                className="h-8 w-8 p-0"
              >
                <ChevronDown className="h-3 w-3" />
              </Button>
            </div>
          </div>

          {showReplace && (
            <div className="flex items-center gap-2">
              <div className="relative flex-1">
                <Input
                  ref={replaceInputRef}
                  type="text"
                  placeholder="Replace..."
                  value={replaceTerm}
                  onChange={(e) => setReplaceTerm(e.target.value)}
                  onKeyDown={handleReplaceKeyDown}
                  className="h-8 text-sm"
                />
              </div>
              <div className="flex items-center gap-1">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={replaceCurrentMatch}
                  disabled={findResult.totalMatches === 0 || !replaceTerm || findResult.currentMatchIndex < 0}
                  className="h-8 px-2 text-xs"
                >
                  Replace
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={replaceAllMatches}
                  disabled={findResult.totalMatches === 0 || !replaceTerm}
                  className="h-8 px-2 text-xs"
                >
                  All
                </Button>
              </div>
            </div>
          )}

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setCaseSensitive(!caseSensitive)}
                className={cn("h-8 w-8 p-0", caseSensitive && "bg-accent text-accent-foreground")}
              >
                <CaseSensitive className="h-3 w-3" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowReplace(!showReplace)}
                className={cn("h-8 w-8 p-0", showReplace && "bg-accent text-accent-foreground")}
              >
                <Replace className="h-3 w-3" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowResults(!showResults)}
                disabled={findResult.totalMatches === 0}
                className={cn("h-8 w-8 p-0", showResults && "bg-accent text-accent-foreground")}
              >
                <List className="h-3 w-3" />
              </Button>
            </div>
            <div className="flex gap-1">
              <button
                onClick={testSimpleHighlight}
                className="px-2 py-1 bg-red-500 text-white text-xs rounded"
              >
                Test
              </button>
              <button
                onClick={debugCurrentState}
                className="px-2 py-1 bg-blue-500 text-white text-xs rounded"
              >
                Debug
              </button>
              <button
                onClick={testDirectHighlighting}
                className="px-2 py-1 bg-green-500 text-white text-xs rounded"
              >
                Test CSS
              </button>
            </div>
          </div>

          {showResults && findResult.matches.length > 0 && (
            <div className="border-t border-border pt-3">
              <div className="text-xs text-muted-foreground mb-2">
                {findResult.totalMatches} result{findResult.totalMatches !== 1 ? 's' : ''}
              </div>
              <div className="max-h-60 overflow-y-auto space-y-1 pr-1 custom-scrollbar">
                {findResult.matches.map((match, index) => (
                  <button
                    key={`${match.blockId}-${match.textIndex}-${index}`} // Added index for more unique key
                    onClick={() => goToMatch(index)}
                    className={cn(
                      "w-full text-left p-2 rounded text-xs hover:bg-accent hover:text-accent-foreground transition-colors",
                      index === findResult.currentMatchIndex && "bg-accent text-accent-foreground"
                    )}
                  >
                    <div className="truncate">
                      {match.blockText.substring(0, match.textIndex)}
                      <span className="bg-yellow-200 dark:bg-yellow-800 px-1 rounded">
                        {match.text}
                      </span>
                      {match.blockText.substring(match.textIndex + match.length)}
                    </div>
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </TooltipProvider>
  );
};

// Add this function to test direct DOM manipulation
const testDirectHighlighting = useCallback(() => {
  console.log('[FindPanel] 🧪 Testing direct highlighting');

  try {
    // Get all blocks
    const allBlocks = document.querySelectorAll('.bn-block-outer');
    console.log(`[FindPanel] 🧪 Found ${allBlocks.length} blocks for testing`);

    if (allBlocks.length > 0) {
      // Apply test class to first block
      const firstBlock = allBlocks[0];
      console.log(`[FindPanel] 🧪 First block:`, {
        id: firstBlock.getAttribute('data-id'),
        classes: Array.from(firstBlock.classList)
      });

      // Apply test class
      firstBlock.classList.add('test-highlight');
      console.log(`[FindPanel] 🧪 Applied test-highlight class to first block`);

      // Apply our actual highlight classes
      firstBlock.classList.add('find-block-highlight');
      console.log(`[FindPanel] 🧪 Applied find-block-highlight class to first block`);

      // Log the result
      console.log(`[FindPanel] 🧪 First block classes after:`, Array.from(firstBlock.classList));

      // Schedule removal after 3 seconds
      setTimeout(() => {
        firstBlock.classList.remove('test-highlight', 'find-block-highlight');
        console.log(`[FindPanel] 🧪 Removed test classes from first block`);
      }, 3000);
    }
  } catch (error) {
    console.error('[FindPanel] 💥 Error in testDirectHighlighting:', error);
  }
}, []);

// Add this effect to ensure cleanup when the component unmounts
useEffect(() => {
  return () => {
    // Clean up any highlights when the component unmounts
    document.querySelectorAll('.bn-block-outer').forEach(block => {
      block.classList.remove('find-block-highlight', 'find-block-highlight-current');
    });
  };
}, []);





