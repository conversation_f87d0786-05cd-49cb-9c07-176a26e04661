import React, { useState, useEffect, useRef, useCallback } from 'react'; // Added useCallback
import { Block, BlockNoteEditor } from '@blocknote/core'; // Added Block for type usage
import { Input } from '@/ui/input';
import { Button } from '@/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/ui/tooltip';
import {
  Search,
  ChevronUp,
  ChevronDown,
  X,
  Replace,
  List,
  CaseSensitive
} from 'lucide-react';
import { cn } from '@/lib/utils';
import {
  findInDocument,
  navigateToMatch,
  createFindResult,
  getNextMatchIndex,
  getPreviousMatchIndex,
  replaceInDocument,
  replaceAllInDocument,
  extractTextFromBlock,
  highlightAllMatches,
  clearAllHighlights,
  debugDOMStructure,
  type FindResult,
  type FindMatch
} from '../utils/findUtils';

interface FindPanelProps {
  isOpen: boolean;
  onClose: () => void;
  editor: BlockNoteEditor | null;
}

export const FindPanel: React.FC<FindPanelProps> = ({
  isOpen,
  onClose,
  editor
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [replaceTerm, setReplaceTerm] = useState('');
  const [caseSensitive, setCaseSensitive] = useState(false);
  const [showReplace, setShowReplace] = useState(false);
  const [showResults, setShowResults] = useState(true);
  const [findResult, setFindResult] = useState<FindResult>({
    matches: [],
    currentMatchIndex: -1,
    totalMatches: 0
  });

  const searchInputRef = useRef<HTMLInputElement>(null);
  const replaceInputRef = useRef<HTMLInputElement>(null);
  const lastSearchTermRef = useRef<string>('');
  // Use a ref for the debounce timeout ID to persist it across re-renders without causing effect re-runs
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);


  // Use the improved highlighting functions from findUtils
  const applyHighlights = useCallback((matches: FindMatch[], currentIndex: number) => {
    console.log(`[FindPanel] 🎯 applyHighlights called with: ${matches.length} matches, current: ${currentIndex}`);
    highlightAllMatches(matches, currentIndex);
  }, []);

  const clearHighlights = useCallback(() => {
    console.log('[FindPanel] 🧹 clearHighlights called');
    clearAllHighlights();
  }, []);

  const executeSearch = useCallback(() => {
    console.log('[FindPanel] 🔍 executeSearch called with:', {
      searchTerm,
      caseSensitive,
      editorExists: !!editor
    });

    // Always update lastSearchTermRef to track what was searched
    lastSearchTermRef.current = searchTerm;

    if (!editor) {
      console.log('[FindPanel] ⚠️ Search aborted: editor missing');
      clearHighlights();
      setFindResult({ matches: [], currentMatchIndex: -1, totalMatches: 0 });
      return;
    }

    if (!searchTerm.trim()) {
      console.log('[FindPanel] ⚠️ Search aborted: empty search term');
      clearHighlights();
      setFindResult({ matches: [], currentMatchIndex: -1, totalMatches: 0 });
      return;
    }

    console.log('[FindPanel] 📄 Editor document blocks:', editor.document.length);

    try {
      // Perform the search
      console.log('[FindPanel] 🔎 Calling findInDocument with:', searchTerm, caseSensitive);
      const matches = findInDocument(editor.document, searchTerm, caseSensitive);
      console.log('[FindPanel] ✅ findInDocument returned matches:', matches.length);
      console.log('[FindPanel] 📊 Match details:', matches.map(m => ({
        blockId: m.blockId,
        textIndex: m.textIndex,
        text: m.text.substring(0, 30) + (m.text.length > 30 ? '...' : '')
      })));

      // Create result object
      const result = createFindResult(matches, matches.length > 0 ? 0 : -1);
      console.log('[FindPanel] 📋 Created findResult:', result);
      setFindResult(result);

      // Apply highlights
      console.log('[FindPanel] 🎨 Calling applyHighlights with matches:', matches.length);
      applyHighlights(result.matches, result.currentMatchIndex);

      // Navigate to first match if available
      if (result.matches.length > 0 && result.currentMatchIndex !== -1) {
        console.log('[FindPanel] 🚀 Navigating to first match:', result.matches[result.currentMatchIndex]);
        navigateToMatch(editor, result.matches[result.currentMatchIndex]);
      }

      // Show notification about search results and auto-show results if matches found
      if (matches.length === 0) {
        console.log('[FindPanel] 📢 No matches found for:', searchTerm);
      } else {
        console.log('[FindPanel] 📢 Found', matches.length, 'matches for:', searchTerm);
        // Auto-show results when matches are found
        setShowResults(true);
      }
    } catch (error) {
      console.error('[FindPanel] 💥 Error during search execution:', error);
      setFindResult({ matches: [], currentMatchIndex: -1, totalMatches: 0 });
    }
  }, [editor, searchTerm, caseSensitive, clearHighlights, applyHighlights]);

  const goToNext = useCallback(() => {
    if (!editor || findResult.totalMatches === 0) return;
    const nextIndex = getNextMatchIndex(findResult.currentMatchIndex, findResult.totalMatches);
    setFindResult(prev => ({ ...prev, currentMatchIndex: nextIndex }));
    applyHighlights(findResult.matches, nextIndex);
    if (findResult.matches[nextIndex]) {
      navigateToMatch(editor, findResult.matches[nextIndex]);
    }
  }, [editor, findResult, applyHighlights]);

  const goToPrevious = useCallback(() => {
    if (!editor || findResult.totalMatches === 0) return;
    const prevIndex = getPreviousMatchIndex(findResult.currentMatchIndex, findResult.totalMatches);
    setFindResult(prev => ({ ...prev, currentMatchIndex: prevIndex }));
    applyHighlights(findResult.matches, prevIndex);
    if (findResult.matches[prevIndex]) {
      navigateToMatch(editor, findResult.matches[prevIndex]);
    }
  }, [editor, findResult, applyHighlights]);

  const goToMatch = useCallback((matchIndex: number) => {
    if (!editor || !findResult.matches[matchIndex]) return;
    setFindResult(prev => ({ ...prev, currentMatchIndex: matchIndex }));
    applyHighlights(findResult.matches, matchIndex);
    navigateToMatch(editor, findResult.matches[matchIndex]);
  }, [editor, findResult, applyHighlights]);

  const replaceCurrentMatch = useCallback(() => {
    if (!editor || findResult.totalMatches === 0 || findResult.currentMatchIndex === -1 || !replaceTerm) return;
    const currentMatch = findResult.matches[findResult.currentMatchIndex];
    if (!currentMatch) return;
    replaceInDocument(editor, currentMatch, replaceTerm);
    setTimeout(() => executeSearch(), 100); // Re-search
  }, [editor, findResult, replaceTerm, executeSearch]);

  const replaceAllMatches = useCallback(() => {
    if (!editor || findResult.totalMatches === 0 || !replaceTerm) return;
    replaceAllInDocument(editor, findResult.matches, replaceTerm);
    setTimeout(() => executeSearch(), 100); // Re-search
  }, [editor, findResult, replaceTerm, executeSearch]);

  useEffect(() => {
    if (!isOpen) {
      clearHighlights();
      setSearchTerm('');
      setReplaceTerm('');
      setShowReplace(false);
      setShowResults(true);
      setFindResult({ matches: [], currentMatchIndex: -1, totalMatches: 0 });
      lastSearchTermRef.current = '';
    } else if (searchInputRef.current) {
        setTimeout(() => {
            searchInputRef.current?.focus();
            searchInputRef.current?.select();
        }, 100);
    }
  }, [isOpen, clearHighlights]);


  // ✨ CLEANUP EFFECT - Using editor.onChange for BlockNote
  useEffect(() => {
    if (!editor || !lastSearchTermRef.current) {
        // If there was a timeout scheduled from a previous run of this effect, clear it.
        if (debounceTimeoutRef.current) {
            clearTimeout(debounceTimeoutRef.current);
            debounceTimeoutRef.current = null;
        }
        return;
    }

    const cleanupInvalidHighlights = () => {
      if (!editor) return; // Guard: editor might have become null

      const searchText = caseSensitive ? lastSearchTermRef.current : lastSearchTermRef.current.toLowerCase();
      if (!searchText) { // If search text is empty, clear all highlights
        clearHighlights();
        return;
      }

      const currentEditorBlocks = editor.document;
      const blockMap = new Map(currentEditorBlocks.map(b => [b.id, b]));

      document.querySelectorAll('.bn-block-outer.find-block-highlight, .bn-block-outer.find-block-highlight-current').forEach(element => {
        const blockId = element.getAttribute('data-id');
        if (!blockId) return;

        const blockObject = blockMap.get(blockId);

        if (blockObject) {
          const extractedBlockText = extractTextFromBlock(blockObject);
          const textToSearch = caseSensitive ? extractedBlockText : extractedBlockText.toLowerCase();

          if (!textToSearch.includes(searchText)) {
            element.classList.remove('find-block-highlight', 'find-block-highlight-current');
            console.log(`[FindPanel] 🧹 Removed highlight from modified block ID: ${blockId} (using extractTextFromBlock)`);
          }
        } else {
          element.classList.remove('find-block-highlight', 'find-block-highlight-current');
          console.log(`[FindPanel] 🧹 Removed highlight from block ID: ${blockId} (block no longer in editor document)`);
        }
      });
    };

    const debouncedCleanup = () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
      debounceTimeoutRef.current = setTimeout(cleanupInvalidHighlights, 300); // Debounce time
    };

    // Subscribe to BlockNote editor changes
    // editor.onChange returns an unsubscribe function
    const unsubscribe = editor.onChange(() => {
      debouncedCleanup();
    });

    // Cleanup function for when the effect re-runs or component unmounts
    return () => {
      unsubscribe(); // Unsubscribe from editor changes
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current); // Clear any pending debounce timeout
        debounceTimeoutRef.current = null;
      }
      // console.log('[FindPanel] Cleaned up editor change listener for highlights.');
    };
  // IMPORTANT: Add extractTextFromBlock to dependency array if it's not stable (i.e. not a top-level import)
  // Assuming extractTextFromBlock is imported and stable, it doesn't need to be in deps.
  // If it were defined inside FindPanel, it would.
  }, [editor, caseSensitive, lastSearchTermRef, clearHighlights]);

  // Cleanup effect to ensure highlights are removed when component unmounts
  useEffect(() => {
    return () => {
      // Clean up any highlights when the component unmounts
      // This will remove the dynamic CSS styles
      clearAllHighlights();
    };
  }, []);

  const handleSearchKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      if (e.shiftKey) {
        goToPrevious();
      } else if (findResult.totalMatches > 0 && searchTerm === lastSearchTermRef.current) { // Only navigate if search term hasn't changed
        goToNext();
      } else {
        executeSearch();
      }
    } else if (e.key === 'Escape') {
      e.preventDefault();
      onClose();
    }
  };

  const handleReplaceKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      replaceCurrentMatch();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      onClose();
    }
  };

  const testSimpleHighlight = () => {
    const firstBlock = document.querySelector('.bn-block-outer');
    if (firstBlock) {
      firstBlock.classList.add('find-block-highlight-current');
      console.log('✅ Test highlight applied:', firstBlock);
    }
  };

  const debugCurrentState = () => {
    console.log('=== DEBUG STATE ===');
    console.log('Search term:', searchTerm);
    console.log('Last search term ref:', lastSearchTermRef.current);
    console.log('Find result:', findResult);
    console.log('Highlighted elements:', document.querySelectorAll('.find-block-highlight, .find-block-highlight-current').length);

    // Debug DOM structure
    debugDOMStructure();

    // Debug individual matches
    findResult.matches.forEach((match, index) => {
      const element = document.querySelector(`[data-id="${match.blockId}"].bn-block-outer`);
      console.log(`Match ${index} (ID: ${match.blockId}):`, {
        foundInDOM: !!element,
        classes: element ? Array.from(element.classList) : 'NOT_FOUND',
        match: match
      });
    });
  };

  const displayText = (() => {
    // If we have matches, show current position
    if (findResult.totalMatches > 0) {
      return `${findResult.currentMatchIndex + 1} of ${findResult.totalMatches}`;
    }

    // If we searched and found no matches, show "0 matches"
    if (lastSearchTermRef.current && searchTerm === lastSearchTermRef.current) {
      return '0 matches';
    }

    // If no search has been performed yet, show empty
    return '';
  })();

  if (!isOpen) return null;

  return (
    <TooltipProvider delayDuration={100}>
      <div className="fixed top-4 right-4 z-[1000] bg-background border border-border rounded-lg shadow-lg min-w-80 max-w-96">
        <div className="flex items-center justify-between p-3 border-b border-border">
          <div className="flex items-center gap-2">
            <Search className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm font-medium">Find & Replace</span>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose} className="h-6 w-6 p-0">
            <X className="h-3 w-3" />
          </Button>
        </div>

        <div className="p-3 space-y-3">
          <div className="flex items-center gap-2">
            <div className="relative flex-1">
              <Input
                ref={searchInputRef}
                type="text"
                placeholder="Find..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyDown={handleSearchKeyDown}
                className="h-8 text-sm pr-24"
              />
              <div className="absolute right-1 top-1/2 -translate-y-1/2 flex items-center gap-1">
                <span className="text-xs text-muted-foreground min-w-16 text-center">
                  {displayText}
                </span>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={executeSearch}
              className="h-8 px-2 text-xs"
              disabled={!searchTerm.trim()}
            >
              Search
            </Button>
            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={goToPrevious}
                disabled={findResult.totalMatches === 0}
                className="h-8 w-8 p-0"
              >
                <ChevronUp className="h-3 w-3" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={goToNext}
                disabled={findResult.totalMatches === 0}
                className="h-8 w-8 p-0"
              >
                <ChevronDown className="h-3 w-3" />
              </Button>
            </div>
          </div>

          {showReplace && (
            <div className="flex items-center gap-2">
              <div className="relative flex-1">
                <Input
                  ref={replaceInputRef}
                  type="text"
                  placeholder="Replace..."
                  value={replaceTerm}
                  onChange={(e) => setReplaceTerm(e.target.value)}
                  onKeyDown={handleReplaceKeyDown}
                  className="h-8 text-sm"
                />
              </div>
              <div className="flex items-center gap-1">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={replaceCurrentMatch}
                  disabled={findResult.totalMatches === 0 || !replaceTerm || findResult.currentMatchIndex < 0}
                  className="h-8 px-2 text-xs"
                >
                  Replace
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={replaceAllMatches}
                  disabled={findResult.totalMatches === 0 || !replaceTerm}
                  className="h-8 px-2 text-xs"
                >
                  All
                </Button>
              </div>
            </div>
          )}

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setCaseSensitive(!caseSensitive)}
                className={cn("h-8 w-8 p-0", caseSensitive && "bg-accent text-accent-foreground")}
              >
                <CaseSensitive className="h-3 w-3" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowReplace(!showReplace)}
                className={cn("h-8 w-8 p-0", showReplace && "bg-accent text-accent-foreground")}
              >
                <Replace className="h-3 w-3" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowResults(!showResults)}
                disabled={findResult.totalMatches === 0}
                className={cn("h-8 w-8 p-0", showResults && "bg-accent text-accent-foreground")}
              >
                <List className="h-3 w-3" />
              </Button>
            </div>
            <div className="flex gap-1">
              <button
                onClick={testSimpleHighlight}
                className="px-2 py-1 bg-red-500 text-white text-xs rounded"
              >
                Test
              </button>
              <button
                onClick={debugCurrentState}
                className="px-2 py-1 bg-blue-500 text-white text-xs rounded"
              >
                Debug
              </button>

            </div>
          </div>

          {showResults && findResult.matches.length > 0 && (
            <div className="border-t border-border pt-3">
              <div className="text-xs text-muted-foreground mb-2">
                {findResult.totalMatches} result{findResult.totalMatches !== 1 ? 's' : ''}
              </div>
              <div className="max-h-60 overflow-y-auto space-y-1 pr-1 custom-scrollbar">
                {findResult.matches.map((match, index) => (
                  <button
                    key={`${match.blockId}-${match.textIndex}-${index}`} // Added index for more unique key
                    onClick={() => goToMatch(index)}
                    className={cn(
                      "w-full text-left p-2 rounded text-xs hover:bg-accent hover:text-accent-foreground transition-colors",
                      index === findResult.currentMatchIndex && "bg-accent text-accent-foreground"
                    )}
                  >
                    <div className="truncate">
                      {match.blockText.substring(0, match.textIndex)}
                      <span className="bg-yellow-200 dark:bg-yellow-800 px-1 rounded">
                        {match.text}
                      </span>
                      {match.blockText.substring(match.textIndex + match.length)}
                    </div>
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </TooltipProvider>
  );
};





