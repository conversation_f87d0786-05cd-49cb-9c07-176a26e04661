import { Block, BlockNoteEditor } from '@blocknote/core';

export interface FindMatch {
  blockId: string;
  blockIndex: number;
  textIndex: number;
  length: number;
  text: string;
  blockText: string;
}

export interface FindResult {
  matches: FindMatch[];
  currentMatchIndex: number;
  totalMatches: number;
}

/**
 * Extract text content from a BlockNote block
 */
export function extractTextFromBlock(block: Block): string {
  try {
    console.log(`[findUtils] 📄 Extracting text from block: ${block.id}`);

    // Log block structure for debugging
    console.log(`[findUtils] 📋 Block type: ${block.type}`);

    let text = '';

    // Handle different block types
    if (block.type === 'paragraph' || block.type === 'heading') {
      if (block.content) {
        text = block.content.map(item => {
          if (typeof item === 'string') return item;
          return item.text || '';
        }).join('');
      }
    } else if (block.type === 'bulletListItem' || block.type === 'numberedListItem') {
      if (block.content) {
        text = block.content.map(item => {
          if (typeof item === 'string') return item;
          return item.text || '';
        }).join('');
      }
    }
    // Add more block types as needed

    console.log(`[findUtils] ✅ Extracted text (${text.length} chars): "${text.substring(0, 50)}${text.length > 50 ? '...' : ''}"`);
    return text;
  } catch (error) {
    console.error('[findUtils] 💥 Error extracting text from block:', error);
    return '';
  }
}

/**
 * Extract all text content from BlockNote document
 */
export function extractAllTextFromDocument(blocks: Block[]): string {
  return blocks
    .map(block => extractTextFromBlock(block))
    .join('\n');
}

/**
 * Find all matches of a search term in the document
 */
export function findInDocument(
  blocks: Block[],
  searchTerm: string,
  caseSensitive: boolean = false
): FindMatch[] {
  console.log(`[findUtils] 🔍 findInDocument called with: "${searchTerm}", caseSensitive: ${caseSensitive}`);
  console.log(`[findUtils] 📄 Processing ${blocks.length} blocks`);

  if (!searchTerm.trim()) {
    console.log('[findUtils] ⚠️ Empty search term, returning empty results');
    return [];
  }

  try {
    const matches: FindMatch[] = [];
    const term = caseSensitive ? searchTerm : searchTerm.toLowerCase();

    blocks.forEach((block, blockIndex) => {
      console.log(`[findUtils] 📑 Processing block ${blockIndex}/${blocks.length} (ID: ${block.id})`);

      // Extract text from the block
      const blockText = extractTextFromBlock(block);
      console.log(`[findUtils] 📝 Block text (${blockText.length} chars): "${blockText.substring(0, 50)}${blockText.length > 50 ? '...' : ''}"`);

      const textToSearch = caseSensitive ? blockText : blockText.toLowerCase();

      // Find all occurrences of the search term in this block
      let index = textToSearch.indexOf(term);
      let matchCount = 0;

      while (index !== -1) {
        matchCount++;
        matches.push({
          blockId: block.id,
          textIndex: index,
          text: blockText
        });

        // Move to next occurrence
        index = textToSearch.indexOf(term, index + 1);
      }

      console.log(`[findUtils] ✅ Found ${matchCount} matches in this block`);
    });

    console.log(`[findUtils] 🎯 Total matches found: ${matches.length}`);
    return matches;
  } catch (error) {
    console.error('[findUtils] 💥 Error in findInDocument:', error);
    return [];
  }
}

/**
 * Navigate to a specific match in the editor
 */
export function navigateToMatch(
  editor: BlockNoteEditor,
  match: FindMatch
): void {
  try {
    // Find the block by ID
    const block = editor.document.find(b => b.id === match.blockId);
    if (!block) {
      console.warn('Block not found for match:', match.blockId);
      return;
    }

    // Set cursor position to the beginning of the match
    editor.setTextCursorPosition(block, "start");

    // Try to scroll the block into view
    const blockElement = document.querySelector(`[data-id="${match.blockId}"]`);
    if (blockElement) {
      blockElement.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      });
    }
  } catch (error) {
    console.error('Error navigating to match:', error);
  }
}

// Store highlighted block IDs for efficient cleanup
const highlightedBlockIds = new Set<string>();

/**
 * Simple and direct highlighting function that works like the test function
 */
export function highlightAllMatches(
  matches: FindMatch[],
  currentMatchIndex: number = -1
): void {
  try {
    // Clear all existing highlights first
    const allBlocks = document.querySelectorAll('.bn-block-outer');
    allBlocks.forEach(block => {
      block.classList.remove('find-block-highlight', 'find-block-highlight-current');
    });

    // Get unique block IDs from matches
    const blockIds = new Set(matches.map(match => match.blockId));
    const currentBlockId = currentMatchIndex >= 0 ? matches[currentMatchIndex]?.blockId : null;

    // Apply highlighting to each unique block - simple and direct
    blockIds.forEach(blockId => {
      const blockElement = document.querySelector(`.bn-block-outer[data-id="${blockId}"]`);
      if (blockElement) {
        const isCurrent = blockId === currentBlockId;
        const className = isCurrent ? 'find-block-highlight-current' : 'find-block-highlight';
        blockElement.classList.add(className);
        console.log(`[findUtils] Applied ${className} to block ${blockId}`);
      } else {
        console.warn(`[findUtils] Block element not found for ID: ${blockId}`);
      }
    });
  } catch (error) {
    console.error('Error highlighting matches:', error);
  }
}

/**
 * Simple function to clear all highlights - just remove the classes directly
 */
export function clearAllHighlights(): void {
  try {
    const allBlocks = document.querySelectorAll('.bn-block-outer');
    allBlocks.forEach(block => {
      block.classList.remove('find-block-highlight', 'find-block-highlight-current');
    });
  } catch (error) {
    console.error('Error clearing highlights:', error);
  }
}

/**
 * Add border to a block element using CSS
 */
function addBlockBorder(element: HTMLElement, isCurrent: boolean = false): void {
  try {
    // Remove any existing border classes
    element.classList.remove('find-block-highlight', 'find-block-highlight-current');

    // Add appropriate border class
    const className = isCurrent ? 'find-block-highlight-current' : 'find-block-highlight';
    element.classList.add(className);
  } catch (error) {
    console.error('Error adding block border:', error);
  }
}

/**
 * Remove border from a block element
 */
function removeBlockBorder(element: HTMLElement): void {
  try {
    element.classList.remove('find-block-highlight', 'find-block-highlight-current');
  } catch (error) {
    console.error('Error removing block border:', error);
  }
}

/**
 * Remove highlights when text changes and no longer matches
 */
export function cleanupInvalidHighlights(
  searchTerm: string,
  caseSensitive: boolean = false
): void {
  try {
    if (!searchTerm.trim()) {
      clearAllHighlights();
      return;
    }

    const searchText = caseSensitive ? searchTerm : searchTerm.toLowerCase();
    const blocksToRemove: string[] = [];

    // Check each highlighted block to see if it still contains the search term
    highlightedBlockIds.forEach(blockId => {
      const blockElement = document.querySelector(`[data-id="${blockId}"].bn-block-outer`);
      if (!blockElement) {
        blocksToRemove.push(blockId);
        return;
      }

      // Get the text content of the block
      const blockText = blockElement.textContent || '';
      const textToSearch = caseSensitive ? blockText : blockText.toLowerCase();

      // If the block no longer contains the search term, remove highlighting
      if (!textToSearch.includes(searchText)) {
        removeBlockBorder(blockElement as HTMLElement);
        blocksToRemove.push(blockId);
      }
    });

    // Remove invalid block IDs from the set
    blocksToRemove.forEach(blockId => {
      highlightedBlockIds.delete(blockId);
    });
  } catch (error) {
    console.error('Error cleaning up invalid highlights:', error);
  }
}

/**
 * Create a find result object
 */
export function createFindResult(
  matches: FindMatch[],
  currentIndex: number = 0
): FindResult {
  return {
    matches,
    currentMatchIndex: Math.max(0, Math.min(currentIndex, matches.length - 1)),
    totalMatches: matches.length
  };
}

/**
 * Get the next match index (wraps around)
 */
export function getNextMatchIndex(currentIndex: number, totalMatches: number): number {
  if (totalMatches === 0) return -1;
  return (currentIndex + 1) % totalMatches;
}

/**
 * Get the previous match index (wraps around)
 */
export function getPreviousMatchIndex(currentIndex: number, totalMatches: number): number {
  if (totalMatches === 0) return -1;
  return currentIndex <= 0 ? totalMatches - 1 : currentIndex - 1;
}

/**
 * Replace a specific match in the document
 */
export function replaceInDocument(
  editor: BlockNoteEditor,
  match: FindMatch,
  replacementText: string
): void {
  try {
    // Find the block by ID
    const block = editor.document.find(b => b.id === match.blockId);
    if (!block) {
      console.warn('Block not found for replacement:', match.blockId);
      return;
    }

    // Get the current block content
    if (!block.content || !Array.isArray(block.content)) {
      console.warn('Block has no content array:', match.blockId);
      return;
    }

    // Create a new content array with the replacement
    const newContent = block.content.map(item => {
      if (item.type === 'text' && (item as any).text) {
        const text = (item as any).text;
        const beforeMatch = text.substring(0, match.textIndex);
        const afterMatch = text.substring(match.textIndex + match.length);

        // Only replace if this text item contains the match
        if (text.includes(match.text) &&
            text.substring(match.textIndex, match.textIndex + match.length) === match.text) {
          return {
            ...item,
            text: beforeMatch + replacementText + afterMatch
          };
        }
      }
      return item;
    });

    // Update the block with new content
    editor.updateBlock(block, {
      ...block,
      content: newContent
    } as any);

    console.log('[findUtils] Replaced text in block:', match.blockId);
  } catch (error) {
    console.error('Error replacing text in document:', error);
  }
}

/**
 * Replace all matches in the document
 */
export function replaceAllInDocument(
  editor: BlockNoteEditor,
  matches: FindMatch[],
  replacementText: string
): void {
  try {
    // Group matches by block ID to batch updates
    const matchesByBlock = new Map<string, FindMatch[]>();

    matches.forEach(match => {
      if (!matchesByBlock.has(match.blockId)) {
        matchesByBlock.set(match.blockId, []);
      }
      matchesByBlock.get(match.blockId)!.push(match);
    });

    // Process each block
    matchesByBlock.forEach((blockMatches, blockId) => {
      const block = editor.document.find(b => b.id === blockId);
      if (!block || !block.content || !Array.isArray(block.content)) {
        console.warn('Block not found or has no content for replacement:', blockId);
        return;
      }

      // Sort matches by text index in descending order to replace from end to start
      // This prevents index shifting issues
      const sortedMatches = [...blockMatches].sort((a, b) => b.textIndex - a.textIndex);

      // Create new content with all replacements
      const newContent = block.content.map(item => {
        if (item.type === 'text' && (item as any).text) {
          let text = (item as any).text;

          // Apply all replacements for this text item
          sortedMatches.forEach(match => {
            if (text.substring(match.textIndex, match.textIndex + match.length) === match.text) {
              const beforeMatch = text.substring(0, match.textIndex);
              const afterMatch = text.substring(match.textIndex + match.length);
              text = beforeMatch + replacementText + afterMatch;
            }
          });

          return {
            ...item,
            text
          };
        }
        return item;
      });

      // Update the block
      editor.updateBlock(block, {
        ...block,
        content: newContent
      } as any);
    });

    console.log('[findUtils] Replaced all matches:', matches.length);
  } catch (error) {
    console.error('Error replacing all matches in document:', error);
  }
}


