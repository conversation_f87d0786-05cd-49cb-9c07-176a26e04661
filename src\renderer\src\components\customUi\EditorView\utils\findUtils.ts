import { Block, BlockNoteEditor } from '@blocknote/core';

export interface FindMatch {
  blockId: string;
  blockIndex: number;
  textIndex: number;
  length: number;
  text: string;
  blockText: string;
}

export interface FindResult {
  matches: FindMatch[];
  currentMatchIndex: number;
  totalMatches: number;
}

/**
 * Extract text content from a BlockNote block
 */
export function extractTextFromBlock(block: Block): string {
  try {
    console.log(`[findUtils] 📄 Extracting text from block: ${block.id}`);

    // Log block structure for debugging
    console.log(`[findUtils] 📋 Block type: ${block.type}`, block);

    let text = '';

    // Handle different block types with improved content extraction
    if (block.content && Array.isArray(block.content)) {
      text = block.content.map(item => {
        if (typeof item === 'string') {
          return item;
        }
        if (typeof item === 'object' && item !== null) {
          // Handle inline content objects
          if ('text' in item && typeof item.text === 'string') {
            return item.text;
          }
          // Handle other content types
          if ('type' in item && item.type === 'text' && 'text' in item) {
            return (item as any).text;
          }
        }
        return '';
      }).join('');
    }

    // Fallback: try to get text from the block's textContent if available
    if (!text && typeof (block as any).textContent === 'string') {
      text = (block as any).textContent;
    }

    console.log(`[findUtils] ✅ Extracted text (${text.length} chars): "${text.substring(0, 50)}${text.length > 50 ? '...' : ''}"`);
    return text;
  } catch (error) {
    console.error('[findUtils] 💥 Error extracting text from block:', error);
    return '';
  }
}

/**
 * Extract all text content from BlockNote document
 */
export function extractAllTextFromDocument(blocks: Block[]): string {
  return blocks
    .map(block => extractTextFromBlock(block))
    .join('\n');
}

/**
 * Find all matches of a search term in the document
 */
export function findInDocument(
  blocks: Block[],
  searchTerm: string,
  caseSensitive: boolean = false
): FindMatch[] {
  console.log(`[findUtils] 🔍 findInDocument called with: "${searchTerm}", caseSensitive: ${caseSensitive}`);
  console.log(`[findUtils] 📄 Processing ${blocks.length} blocks`);

  if (!searchTerm.trim()) {
    console.log('[findUtils] ⚠️ Empty search term, returning empty results');
    return [];
  }

  try {
    const matches: FindMatch[] = [];
    const term = caseSensitive ? searchTerm : searchTerm.toLowerCase();

    blocks.forEach((block, blockIndex) => {
      console.log(`[findUtils] 📑 Processing block ${blockIndex}/${blocks.length} (ID: ${block.id})`);

      // Extract text from the block
      const blockText = extractTextFromBlock(block);
      console.log(`[findUtils] 📝 Block text (${blockText.length} chars): "${blockText.substring(0, 50)}${blockText.length > 50 ? '...' : ''}"`);

      const textToSearch = caseSensitive ? blockText : blockText.toLowerCase();

      // Find all occurrences of the search term in this block
      let index = textToSearch.indexOf(term);
      let matchCount = 0;

      while (index !== -1) {
        matchCount++;
        matches.push({
          blockId: block.id,
          blockIndex: blockIndex,
          textIndex: index,
          length: term.length,
          text: blockText.substring(index, index + term.length), // Extract the actual matched text
          blockText: blockText
        });

        // Move to next occurrence
        index = textToSearch.indexOf(term, index + 1);
      }

      console.log(`[findUtils] ✅ Found ${matchCount} matches in this block`);
    });

    console.log(`[findUtils] 🎯 Total matches found: ${matches.length}`);
    return matches;
  } catch (error) {
    console.error('[findUtils] 💥 Error in findInDocument:', error);
    return [];
  }
}

/**
 * Navigate to a specific match in the editor
 */
export function navigateToMatch(
  editor: BlockNoteEditor,
  match: FindMatch
): void {
  try {
    // Find the block by ID
    const block = editor.document.find(b => b.id === match.blockId);
    if (!block) {
      console.warn('Block not found for match:', match.blockId);
      return;
    }

    // Set cursor position to the beginning of the match
    editor.setTextCursorPosition(block, "start");

    // Try to scroll the block into view
    const blockElement = document.querySelector(`[data-id="${match.blockId}"]`);
    if (blockElement) {
      blockElement.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      });
    }
  } catch (error) {
    console.error('Error navigating to match:', error);
  }
}

// Store highlighted block IDs for efficient cleanup
const highlightedBlockIds = new Set<string>();

/**
 * Modern highlighting function with improved DOM selection and error handling
 */
export function highlightAllMatches(
  matches: FindMatch[],
  currentMatchIndex: number = -1
): void {
  try {
    console.log(`[findUtils] 🎨 Highlighting ${matches.length} matches, current: ${currentMatchIndex}`);

    // Clear all existing highlights first
    clearAllHighlights();

    if (matches.length === 0) {
      console.log('[findUtils] No matches to highlight');
      return;
    }

    // Get unique block IDs from matches
    const blockIds = new Set(matches.map(match => match.blockId));
    const currentBlockId = currentMatchIndex >= 0 ? matches[currentMatchIndex]?.blockId : null;

    console.log(`[findUtils] Highlighting ${blockIds.size} unique blocks`);

    // Apply highlighting to each unique block with multiple selector strategies
    blockIds.forEach(blockId => {
      // Try multiple selector strategies for better compatibility
      const selectors = [
        `[data-id="${blockId}"].bn-block-outer`,
        `[data-id="${blockId}"]`,
        `.bn-block-outer[data-id="${blockId}"]`
      ];

      let blockElement: Element | null = null;

      for (const selector of selectors) {
        blockElement = document.querySelector(selector);
        if (blockElement) break;
      }

      if (blockElement) {
        const isCurrent = blockId === currentBlockId;
        const className = isCurrent ? 'find-block-highlight-current' : 'find-block-highlight';
        blockElement.classList.add(className);
        console.log(`[findUtils] ✅ Applied ${className} to block ${blockId}`);
      } else {
        console.warn(`[findUtils] ⚠️ Block element not found for ID: ${blockId}`);
        // Debug: log all available data-id elements
        const allDataIdElements = document.querySelectorAll('[data-id]');
        console.log(`[findUtils] 🔍 Available data-id elements:`,
          Array.from(allDataIdElements).slice(0, 5).map(el => ({
            id: el.getAttribute('data-id'),
            classes: Array.from(el.classList),
            tagName: el.tagName
          }))
        );
      }
    });
  } catch (error) {
    console.error('[findUtils] 💥 Error highlighting matches:', error);
  }
}

/**
 * Modern function to clear all highlights with improved selector strategy
 */
export function clearAllHighlights(): void {
  try {
    console.log('[findUtils] 🧹 Clearing all highlights');

    // Use multiple selector strategies to ensure we catch all highlighted elements
    const selectors = [
      '.find-block-highlight',
      '.find-block-highlight-current',
      '.bn-block-outer.find-block-highlight',
      '.bn-block-outer.find-block-highlight-current',
      '[data-id].find-block-highlight',
      '[data-id].find-block-highlight-current'
    ];

    selectors.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      elements.forEach(element => {
        element.classList.remove('find-block-highlight', 'find-block-highlight-current');
      });
    });

    console.log('[findUtils] ✅ Cleared all highlights');
  } catch (error) {
    console.error('[findUtils] 💥 Error clearing highlights:', error);
  }
}

/**
 * Remove highlights when text changes and no longer matches
 */
export function cleanupInvalidHighlights(
  searchTerm: string,
  caseSensitive: boolean = false
): void {
  try {
    if (!searchTerm.trim()) {
      clearAllHighlights();
      return;
    }

    const searchText = caseSensitive ? searchTerm : searchTerm.toLowerCase();
    const blocksToRemove: string[] = [];

    // Check each highlighted block to see if it still contains the search term
    highlightedBlockIds.forEach(blockId => {
      const blockElement = document.querySelector(`[data-id="${blockId}"].bn-block-outer`);
      if (!blockElement) {
        blocksToRemove.push(blockId);
        return;
      }

      // Get the text content of the block
      const blockText = blockElement.textContent || '';
      const textToSearch = caseSensitive ? blockText : blockText.toLowerCase();

      // If the block no longer contains the search term, remove highlighting
      if (!textToSearch.includes(searchText)) {
        blockElement.classList.remove('find-block-highlight', 'find-block-highlight-current');
        blocksToRemove.push(blockId);
      }
    });

    // Remove invalid block IDs from the set
    blocksToRemove.forEach(blockId => {
      highlightedBlockIds.delete(blockId);
    });
  } catch (error) {
    console.error('Error cleaning up invalid highlights:', error);
  }
}

/**
 * Create a find result object with improved index handling
 */
export function createFindResult(
  matches: FindMatch[],
  currentIndex: number = 0
): FindResult {
  const validCurrentIndex = matches.length > 0
    ? Math.max(0, Math.min(currentIndex, matches.length - 1))
    : -1;

  return {
    matches,
    currentMatchIndex: validCurrentIndex,
    totalMatches: matches.length
  };
}

/**
 * Get the next match index (wraps around)
 */
export function getNextMatchIndex(currentIndex: number, totalMatches: number): number {
  if (totalMatches === 0) return -1;
  return (currentIndex + 1) % totalMatches;
}

/**
 * Get the previous match index (wraps around)
 */
export function getPreviousMatchIndex(currentIndex: number, totalMatches: number): number {
  if (totalMatches === 0) return -1;
  return currentIndex <= 0 ? totalMatches - 1 : currentIndex - 1;
}

/**
 * Replace a specific match in the document
 */
export function replaceInDocument(
  editor: BlockNoteEditor,
  match: FindMatch,
  replacementText: string
): void {
  try {
    // Find the block by ID
    const block = editor.document.find(b => b.id === match.blockId);
    if (!block) {
      console.warn('Block not found for replacement:', match.blockId);
      return;
    }

    // Get the current block content
    if (!block.content || !Array.isArray(block.content)) {
      console.warn('Block has no content array:', match.blockId);
      return;
    }

    // Create a new content array with the replacement
    const newContent = block.content.map(item => {
      if (item.type === 'text' && (item as any).text) {
        const text = (item as any).text;
        const beforeMatch = text.substring(0, match.textIndex);
        const afterMatch = text.substring(match.textIndex + match.length);

        // Only replace if this text item contains the match
        if (text.includes(match.text) &&
            text.substring(match.textIndex, match.textIndex + match.length) === match.text) {
          return {
            ...item,
            text: beforeMatch + replacementText + afterMatch
          };
        }
      }
      return item;
    });

    // Update the block with new content
    editor.updateBlock(block, {
      ...block,
      content: newContent
    } as any);

    console.log('[findUtils] Replaced text in block:', match.blockId);
  } catch (error) {
    console.error('Error replacing text in document:', error);
  }
}

/**
 * Replace all matches in the document
 */
export function replaceAllInDocument(
  editor: BlockNoteEditor,
  matches: FindMatch[],
  replacementText: string
): void {
  try {
    // Group matches by block ID to batch updates
    const matchesByBlock = new Map<string, FindMatch[]>();

    matches.forEach(match => {
      if (!matchesByBlock.has(match.blockId)) {
        matchesByBlock.set(match.blockId, []);
      }
      matchesByBlock.get(match.blockId)!.push(match);
    });

    // Process each block
    matchesByBlock.forEach((blockMatches, blockId) => {
      const block = editor.document.find(b => b.id === blockId);
      if (!block || !block.content || !Array.isArray(block.content)) {
        console.warn('Block not found or has no content for replacement:', blockId);
        return;
      }

      // Sort matches by text index in descending order to replace from end to start
      // This prevents index shifting issues
      const sortedMatches = [...blockMatches].sort((a, b) => b.textIndex - a.textIndex);

      // Create new content with all replacements
      const newContent = block.content.map(item => {
        if (item.type === 'text' && (item as any).text) {
          let text = (item as any).text;

          // Apply all replacements for this text item
          sortedMatches.forEach(match => {
            if (text.substring(match.textIndex, match.textIndex + match.length) === match.text) {
              const beforeMatch = text.substring(0, match.textIndex);
              const afterMatch = text.substring(match.textIndex + match.length);
              text = beforeMatch + replacementText + afterMatch;
            }
          });

          return {
            ...item,
            text
          };
        }
        return item;
      });

      // Update the block
      editor.updateBlock(block, {
        ...block,
        content: newContent
      } as any);
    });

    console.log('[findUtils] Replaced all matches:', matches.length);
  } catch (error) {
    console.error('Error replacing all matches in document:', error);
  }
}

/**
 * Debug function to analyze DOM structure for troubleshooting
 */
export function debugDOMStructure(): void {
  console.log('[findUtils] 🔍 DOM Structure Analysis:');

  // Check for BlockNote elements
  const bnElements = document.querySelectorAll('[class*="bn-"]');
  console.log(`Found ${bnElements.length} BlockNote elements`);

  // Check for data-id elements
  const dataIdElements = document.querySelectorAll('[data-id]');
  console.log(`Found ${dataIdElements.length} elements with data-id`);

  // Sample the first few elements
  const sampleElements = Array.from(dataIdElements).slice(0, 5);
  console.log('Sample elements:', sampleElements.map(el => ({
    id: el.getAttribute('data-id'),
    tagName: el.tagName,
    classes: Array.from(el.classList),
    textContent: el.textContent?.substring(0, 50) + '...'
  })));

  // Check for existing highlights
  const highlightedElements = document.querySelectorAll('.find-block-highlight, .find-block-highlight-current');
  console.log(`Found ${highlightedElements.length} highlighted elements`);
}


